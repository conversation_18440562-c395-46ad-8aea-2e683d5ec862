import { clsx } from "clsx";
import styles from "./LanguageToggle.module.scss";
import { useCallback, useEffect, useMemo, useState } from "react";
import { ReactComponent as Globe } from "./icons/globe.svg";
import { atom, useSetAtom } from "jotai";
import { useTranslation } from "react-i18next";

interface LanguageToggleProps {
  className?: string;
}

const LANGUAGE_OPTIONS = [
  { code: "en-US", label: "English", shortLabel: "EN" },
  { code: "zh-CN", label: "中文", shortLabel: "中" },
];

const PREFERRED_LANGUAGE_KEY = "i18nextLng";

export const getCurrentLanguage = () => {
  const savedLanguage = window.localStorage.getItem(PREFERRED_LANGUAGE_KEY) ?? LANGUAGE_OPTIONS[0].code;
  return LANGUAGE_OPTIONS.find(lang => lang.code === savedLanguage) || LANGUAGE_OPTIONS[0];
};

export const languageAtom = atom<string>(getCurrentLanguage().code);

export const LanguageToggle: React.FC<LanguageToggleProps> = ({ className }) => {
  const { i18n, t } = useTranslation();
  const currentLanguage = getCurrentLanguage();
  const [language, setLanguage] = useState(currentLanguage);
  const setLanguageAtom = useSetAtom(languageAtom);

  useEffect(() => {
    // Sync with i18n current language
    const currentLang = LANGUAGE_OPTIONS.find(lang => lang.code === i18n.language) || LANGUAGE_OPTIONS[0];
    setLanguage(currentLang);
  }, [i18n.language]);

  const languageChanged = useCallback(() => {
    const currentIndex = LANGUAGE_OPTIONS.findIndex(lang => lang.code === language.code);
    const nextIndex = (currentIndex + 1) % LANGUAGE_OPTIONS.length;
    const nextLanguage = LANGUAGE_OPTIONS[nextIndex];

    // Change language in i18n
    i18n.changeLanguage(nextLanguage.code);
    
    // Update local state
    setLanguage(nextLanguage);
    setLanguageAtom(nextLanguage.code);
  }, [language, i18n, setLanguageAtom]);

  const languageLabel = useMemo(
    () => language.shortLabel,
    [language],
  );

  return (
    <button
      className={clsx(styles.languageToggle, className)}
      onClick={languageChanged}
      type="button"
      title={t('language.switchLanguage')}
    >
      <div className={clsx(styles.languageToggle__icon)}>
        <Globe className={clsx(styles.globe)} />
      </div>
      <span className={clsx(styles.languageToggle__label)}>{languageLabel}</span>
    </button>
  );
};

export default LanguageToggle;
