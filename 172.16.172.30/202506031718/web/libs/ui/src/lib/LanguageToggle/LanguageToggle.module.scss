.languageToggle {
  --button-background-color:  var(--color-neutral-background);
  --button-border-color:  var(--color-neutral-border);
  --button-background-color-hover:  var(--color-neutral-surface);
  --button-border-color-hover:  var(--color-neutral-border-bold);
  --button-background-color-active:  var(--color-neutral-surface-active);
  --button-border-color-active:  var(--color-neutral-border-bold);
  --button-text-color:  var(--color-neutral-content);
  --button-icon-color: var(--color-neutral-content);
  --button-font-size: var(--font-size-16);

  display: flex;
  justify-content: center;
  overflow: hidden;
  height: 32px;
  align-self: center;
  font-size: var(--button-font-size);
  border: 1px solid var(--button-border-color);
  border-radius: 24px;
  background: var(--button-background-color);
  padding: 4px 8px 4px 0;
  transition: all 150ms ease-out;
  cursor: pointer;

  &:hover {
    background: var(--button-background-color-hover);
    border-color: var(--button-border-color-hover);
  }

  &:active {
    background: var(--button-background-color-active);
    border-color: var(--button-border-color-active);
  }

  &__label {
    display: flex;
    height: 100%;
    align-items: center;
    color: var(--button-text-color);
    width: 32px;
    justify-content: center;
    font-weight: 500;
  }

  &__icon {
    display: flex;
    align-items: center;
    width: 36px;
    height: 28px;
    margin-right: 0;
    position: relative;
    align-self: center;
    color: var(--button-icon-color);
    justify-content: center;
  }
}

.globe {
  width: 18px;
  height: 18px;
  transition: transform 300ms ease-out;
  
  &:hover {
    transform: scale(1.1);
  }
}
