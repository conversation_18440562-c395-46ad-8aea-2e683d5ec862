import { clsx } from "clsx";
import styles from "./ThemeToggle.module.scss";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { ReactComponent as Sun } from "./icons/sun.svg";
import { ReactComponent as Moon } from "./icons/moon.svg";
import { Badge } from "@humansignal/ui";
import { atom, useSetAtom } from "jotai";

interface ThemeToggleProps {
  className?: string;
}

const THEME_OPTIONS = ["auto", "light", "dark"];
const PREFERRED_COLOR_SCHEME_KEY = "preferred-color-scheme";

export const getCurrentTheme = () => {
  const themeSelection = window.localStorage.getItem(PREFERRED_COLOR_SCHEME_KEY) ?? THEME_OPTIONS[0];
  return themeSelection === THEME_OPTIONS[0]
    ? window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light"
    : themeSelection;
};
export const themeAtom = atom<string>(getCurrentTheme());
export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className }) => {
  const { t } = useTranslation();
  const presetTheme = window.localStorage.getItem(PREFERRED_COLOR_SCHEME_KEY) ?? THEME_OPTIONS[1];
  const [theme, setTheme] = useState(presetTheme);
  const systemMode = useMemo(
    () => (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"),
    [],
  );
  const [appliedTheme, setAppliedTheme] = useState(presetTheme === "auto" ? systemMode : presetTheme);
  const setThemeAtom = useSetAtom(themeAtom);

  useEffect(() => {
    if (!appliedTheme) return;
    document.documentElement.setAttribute("data-color-scheme", appliedTheme.toLowerCase());
  }, [appliedTheme]);

  const themeChanged = useCallback(() => {
    const length = THEME_OPTIONS.length;
    const index = (THEME_OPTIONS.indexOf(theme) + 1) % length;
    const nextTheme = THEME_OPTIONS[index];

    window.localStorage.setItem(PREFERRED_COLOR_SCHEME_KEY, nextTheme);
    setTheme(nextTheme);
    const newTheme = nextTheme === "auto" ? systemMode : nextTheme;
    setAppliedTheme(newTheme);
    setThemeAtom(newTheme);
  }, [theme]);

  const themeLabel = useMemo(
    () => t(`ui.theme.${theme}`),
    [theme, t],
  );

  return (
    <button
      className={clsx(styles.themeToggle, className, {
        [styles.dark]: appliedTheme === "dark",
        [styles.light]: appliedTheme === "light",
      })}
      onClick={themeChanged}
      type="button"
    >
      <div className={clsx(styles.themeToggle__icon)}>
        <div className={clsx(styles.animationWrapper)}>
          <Moon className={clsx(styles.moon)} />
          <Sun className={clsx(styles.sun)} />
        </div>
      </div>
      <span className={clsx(styles.themeToggle__label)}>{themeLabel}</span>
      <Badge variant="beta" className={styles.betaBadge}>
        {t("ui.beta")}
      </Badge>
    </button>
  );
};

export default ThemeToggle;
