import { forwardRef } from "react";
import { Block, Elem } from "../../utils/bem";
import { useTranslation } from "react-i18next";
import "./Label.scss";

export const Label = forwardRef(
  ({ text, children, required, placement, description, size, large, style, simple, flat }, ref) => {
    const { t } = useTranslation();
    const tagName = simple ? "div" : "label";
    const mods = {
      size,
      large,
      flat,
      placement,
      withDescription: !!description,
      empty: !children,
    };

    const combinedStyle = {
      ...style,
      ...(required && {
        '--required-text': `"${t('common.required')}"`,
      }),
    };

    return (
      <Block ref={ref} name="field-label" mod={mods} tag={tagName} style={combinedStyle} data-required={required}>
        <Elem name="text">
          <Elem name="content">
            {text}
            {description && <Elem name="description">{description}</Elem>}
          </Elem>
        </Elem>
        <Elem name="field">{children}</Elem>
      </Block>
    );
  },
);

export default Label;
