.user-info {
  padding: 20px 24px;
  border: 1px solid var(--color-neutral-border);
  background: var(--color-neutral-surface);
  align-self: start;
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 4px;
  min-height: 100%;

  &__close {
    top: 20px;
    right: 24px;
    width: 32px;
    height: 32px;
    position: absolute;

    --button-color: var(--color-primary-content);

    border: none;

    &:hover {
      color: var(--color-primary-content);
      background-color: var(--color-primary-emphasis-subtle);
    }
  }

  &__header {
    display: grid;
    grid-template: auto / 64px auto;
    column-gap: 16px;
    line-height: 1.5;
    color: var(--color-neutral-content);
    margin-bottom: 16px;
    align-items: center;
  }

  &__email {
    margin: 0;
  }

  &__full-name {
    font-size: 1.5em;
    margin: 0;
    font-weight: 500;
    line-height: 1.2;
    color: var(--color-neutral-content);
  }

  &__section {
    & + & {
      margin-top: 16px;
    }

    &-title {
      margin-top: 1em;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--color-neutral-content);
    }
  }

  &__last-active {
    color: var(--color-neutral-content-subtler);
    margin-top: 32px;
    margin-bottom: 0;
  }

  &__links-list {
    display: flex;
    flex-direction: column;
  }

  &__project-link {
    height: 36px;
    display: flex;
    padding: 0 10px;
    font-size: 16px;
    margin-left: -10px;
    align-items: center;
    border-radius: 4px;
    color: var(--color-primary-content);

    &:hover {
      background-color: var(--color-primary-emphasis-subtle);
    }
  }
}