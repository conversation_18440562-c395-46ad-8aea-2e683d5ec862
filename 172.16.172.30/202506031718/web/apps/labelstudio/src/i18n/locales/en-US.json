{"common": {"welcome": "Welcome 👋", "letsGetStarted": "Let's get you started.", "createProject": "Create Project", "invitePeople": "Invite People", "recentProjects": "Recent Projects", "viewAll": "View All", "resources": "Resources", "learnExploreGetHelp": "Learn, explore and get help", "documentation": "Documentation", "apiDocumentation": "API Documentation", "releaseNotes": "Release Notes", "blog": "LabelStud.io Blog", "slackCommunity": "Slack Community", "labelStudioVersion": "Label Studio Version: Community", "cantLoadProjects": "can't load projects", "createFirstProject": "Create your first project", "importDataSetupInterface": "Import your data and set up the labeling interface to start annotating", "projects": "Projects", "create": "Create", "loading": "Loading...", "home": "Home", "settings": "Settings", "tasks": "Tasks", "progress": "Progress", "finished": "finished", "of": "of", "percent": "%", "delete": "Delete", "save": "Save", "saved": "Saved!", "learnMore": "Learn more", "edit": "Edit", "update": "Update", "search": "Search", "filter": "Filter", "sort": "Sort", "actions": "Actions", "name": "Name", "description": "Description", "status": "Status", "date": "Date", "type": "Type", "required": "Required"}, "navigation": {"home": "Home", "projects": "Projects", "settings": "Settings", "organization": "Organization", "dataManager": "Data Manager", "export": "Export", "webhooks": "Webhooks", "accountSettings": "Account & Settings", "logOut": "Log Out", "checkNotificationSettings": "Please check new notification settings in the Account & Settings page", "api": "API", "docs": "Docs", "github": "GitHub", "slackCommunity": "Slack Community", "pinMenu": "Pin menu", "unpinMenu": "Unpin menu"}, "language": {"english": "English", "chinese": "中文", "switchLanguage": "Switch Language"}, "createProject": {"title": "Create Project", "projectName": "Project Name", "description": "Description", "descriptionPlaceholder": "Optional description of your project", "workspace": "Workspace", "selectOption": "Select an option", "workspaceDescription": "Simplify project management by organizing projects into workspaces.", "dataImport": "Data Import", "labelingSetup": "Labeling Setup"}, "export": {"title": "Export data", "export": "Export", "preparingMessage": "Files are being prepared. It might take some time.", "formatInfo": "You can export dataset in one of the following formats:", "cantFindFormat": "Can't find an export format?", "letUsKnow": "Please let us know in", "orSubmitIssue": "or submit an issue to the", "repository": "Repository"}, "settings": {"generalSettings": "General Settings", "color": "Color", "taskSampling": "Task Sampling", "sequential": "Sequential", "sequentialDescription": "Tasks are ordered by Task ID", "random": "Random", "randomDescription": "Tasks are chosen with uniform random", "uncertaintySampling": "Uncertainty sampling", "uncertaintySamplingDescription": "Tasks are chosen according to model uncertainty score (active learning mode)."}, "error": {"askOnSlack": "Ask on Slack", "copied": "<PERSON>pied", "copyStacktrace": "<PERSON><PERSON>", "goBack": "Go Back", "reload": "Reload", "version": "Version:", "errorId": "Error ID:", "heidiDown": "<PERSON>'s down"}, "modal": {"cancel": "Cancel", "ok": "OK"}, "heidiTips": {"dontShow": "Don't show", "didYouKnow": "Did you know?", "learnMore": "Learn more"}, "config": {"emptyConfigTitle": "Your labeling configuration is empty. It is required to label your data.", "emptyConfigDescription": "Start from one of our predefined templates or create your own config on the Code panel. The labeling config is XML-based and you can", "readDocumentation": "read about the available tags in our documentation", "deleteLabel": "Delete label", "addChoices": "Add choices", "addLabelNames": "Add label names", "useNewLineSeparator": "Use new line as a separator to add multiple labels", "add": "Add", "choices": "Choices", "labels": "Labels", "configureSettings": "Configure settings", "configureData": "Configure data", "templateRequiresMoreData": "This template requires more data then you have for now", "needUploadData": "To select which field(s) to label you need to upload the data. Alternatively, you can provide it using Code mode.", "use": "Use", "for": "for", "from": "from", "field": "field", "importedFile": "<imported file>", "setManually": "<set manually>", "labelingInterface": "Labeling Interface", "browseTemplates": "Browse Templates", "code": "Code", "visual": "Visual", "configureTagsDescription": "Configure the labeling interface with tags.", "seeAllTags": "See all available tags", "parserError": "Parser error"}, "dangerZone": {"title": "Danger Zone", "description": "Perform these actions at your own risk. Actions you take on this page can't be reverted. Make sure your data is backed up.", "actionConfirmation": "Action confirmation", "deleteConfirmation": "You're about to delete all things. This action cannot be undone.", "proceed": "Proceed", "deleteAnnotations": "Delete {{count}} Annotations", "deleteTasks": "Delete {{count}} Tasks", "deletePredictions": "Delete {{count}} Predictions", "resetCache": "<PERSON><PERSON>", "resetCacheHelp": "Reset Cache may help in cases like if you are unable to modify the labeling configuration due to validation errors concerning existing labels, but you are confident that the labels don't exist. You can use this action to reset the cache and try again.", "dropAllTabs": "Drop All Tabs", "dropAllTabsHelp": "If the Data Manager is not loading, dropping all Data Manager tabs can help.", "deleteProject": "Delete Project", "deleteProjectHelp": "Deleting a project removes all tasks, annotations, and project data from the database."}, "webhooks": {"title": "Webhooks", "newWebhook": "New Webhook", "editWebhook": "Edit Webhook", "payloadUrl": "Payload URL", "isActive": "Is Active", "headers": "Headers", "headerPlaceholder": "header", "valuePlaceholder": "value", "payload": "Payload", "sendPayload": "Send payload", "sendForAllActions": "Send for all actions", "sendPayloadFor": "Send Payload for", "deleteWebhook": "Delete Webhook", "addWebhook": "Add Webhook", "urlPlaceholder": "URL"}, "organization": {"apiTokensSettings": "API Tokens Settings", "addPeople": "Add People"}, "validation": {"isRequired": "{{fieldName}} is required", "mustMatchPattern": "{{fieldName}} must match the pattern {{pattern}}", "mustBeValidJson": "{{fieldName}} must be valid JSON string", "mustBeValidRegex": "{{fieldName}} must be a valid regular expression"}, "models": {"createModel": "Create a Model", "createModelDescription": "Build a high quality model to auto-label your data using LLMs"}, "ui": {"uploadImage": "Upload Image", "enterprise": "Enterprise", "close": "Close", "search": "Search", "selectAnOption": "Select an option", "noResultsFound": "No results found.", "beta": "Beta", "theme": {"auto": "Auto", "light": "Light", "dark": "Dark"}}}