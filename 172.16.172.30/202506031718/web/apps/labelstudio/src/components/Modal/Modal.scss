.modal {
  --transition-duration: 100ms;

  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  position: fixed;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--color-neutral-shadow-raw) / 70%);
  will-change: opacity;
  backdrop-filter: blur(2px);

  &_optimize &__wrapper {
    will-change: transform;
  }

  &__wrapper {
    width: 100%;
    max-height: 100%;
    padding: 40px 0;
    overflow: auto;
    box-sizing: border-box;
  }

  &__content {
    width: 400px;
    min-width: 400px;
    min-height: 100px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    background-color: var(--color-neutral-background);
    border-radius: 0.5rem;
    box-shadow: 0 10px 30px rgb(var(--color-neutral-shadow-raw) / calc( 30% * var(--shadow-intensity) )), inset 0 1px rgb(255 255 255 / 10%), inset 0 -2px rgba(var(--color-neutral-shadow-raw) / 24% );
    overflow: auto;
  }

  &__header {
    display: flex;
    min-height: 40px;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    box-sizing: content-box;

    &_divided {
      border-bottom: 1px solid var(--color-neutral-border);
    }
  }

  &__title {
    margin: 0;
    margin-right: auto;
    font-size: 1.75rem;
    font-weight: 500;
    color: var(--color-neutral-content);
  }

  &__body {
    flex: 1;
    padding: 0 2rem 2rem;
    min-height: 0;
    line-height: 140%;
    color: var(--color-neutral-content-subtle);

    &_bare {
      padding: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  &__footer {
    padding: 1rem 1.5rem;
    text-align: center;
    font-size: 14px;
    line-height: 22px;
    color: var(--color-neutral-content-subtle);

    & a {
      color: var(--color-primary-content);

      &:hover {
        color: var(--color-primary-content-hover);
        text-decoration: underline;
      }
    }

    &:not(.modal__footer_bare) {
      background: var(--color-neutral-surface);
      box-shadow: inset 0 1px 0 var(--color-neutral-border);
    }
  }

  &__close {
    border: none;

    --icon-size: 24px;
  }

  &_fullscreen &__content {
    width: calc(100vw - 80px);
    height: calc(100vh - 80px);
  }

  &_visible {
    opacity: 0;
    transition: opacity var(--transition-duration) ease;
  }

  &_visible &__wrapper {
    transform: scale(1.05);
    transition: transform var(--transition-duration) ease;
  }

  &.visible {
    opacity: 1;
  }

  &.visible &__wrapper {
    transform: none;
  }

  &.before-appear {
    opacity: 0;
  }

  &.before-appear &__wrapper {
    transform: scale(1.05);
  }

  &.appear {
    opacity: 1;
  }

  &.appear &__wrapper {
    transform: scale(1);
  }

  &.before-disappear {
    opacity: 1;
  }

  &.before-disappear &__wrapper {
    transform: scale(1);
  }

  &.disappear {
    opacity: 0;
  }

  &.disappear &__wrapper {
    transform: scale(1.05);
  }
}
