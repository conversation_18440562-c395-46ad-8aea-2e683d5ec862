.description {
  color: var(--color-neutral-content-subtler);
  line-height: 140%;

  & a {
    color: var(--color-primary-content);

    &:hover {
      color: var(--color-primary-content-subtle);
      text-decoration: none;
      text-decoration: underline;
    }
  }

  &_size {
    &_small {
      font-size: 0.75rem;
    }

    &_medium {
      font-size: 0.875rem;
    }

    &_large {
      font-size: 1.125rem;
    }
  }

  &:not(.description_noOffset) {
    &.description_size {
      &_small {
        margin: 16px 0;
      }
  
      &_medium {
        margin: 32px 0;
      }
  
      &_large {
        margin: 40px 0;
      }
    }
  }
}