.input-ls,
.select-ls,
.textarea-ls {
  --input-size: 40px;

  height: var(--input-size);
  min-height: var(--input-size);
  background: var(--color-neutral-background);
  font-size: 16px;
  line-height: 22px;
  border: 1px solid var(--color-neutral-border);
  box-sizing: border-box;
  border-radius: 5px;
  padding: 0 16px;
  transition: all 150ms ease-out;
  font-weight: 400;
  color: var(--color-neutral-content);
  box-shadow: inset 0 1px 2px rgba(var(--color-neutral-shadow-raw) / 8%);

  &::placeholder {
    color: var(--color-neutral-content-subtler);
  }

  &_ghost {
    border: none;
    padding: 0;
    background-color: transparent;
    outline: none;
  }

  &:read-only {
    background-color: var(--color-neutral-surface);
    color: var(--color-neutral-content-subtler);
  }
}

.input-ls,
.textarea-ls {
  &:not([disabled]):hover {
    border-color: var(--color-neutral-border-bold);
  }
  

  &:not([disabled]):active {
    border-color: var(--color-neutral-border-bolder);
  }
}

.select-ls {
  &:not(.disabled):hover {
    border-color: var(--color-neutral-border-bold);
  }

  &:not(.disabled):active {
    border-color: var(--color-neutral-border-bolder);
  }
}

input.input-ls[type="radio"] {
  width: 16px;
  height: 16px;
  min-height: 0;
}

.textarea-ls {
  padding: 12px 16px;
  min-height: 50px;
}