import { isDefined, isEmptyString } from "../../../utils/helpers";
import i18n from "../../../i18n";
import "./Validation.scss";

export const required = (fieldName, value) => {
  if (!isDefined(value) || isEmptyString(value)) {
    return i18n.t("validation.isRequired", { fieldName });
  }
};

export const matchPattern = (pattern) => (fieldName, value) => {
  pattern = typeof pattern === "string" ? new RegExp(pattern) : pattern;

  if (!isEmptyString(value) && value.match(pattern) === null) {
    return i18n.t("validation.mustMatchPattern", { fieldName, pattern });
  }
};

export const json = (fieldName, value) => {
  if (!isDefined(value) || value.trim().length === 0) return;

  if (/^(\{|\[)/.test(value) === false || /(\}|\])$/.test(value) === false) {
    return i18n.t("validation.mustBeValidJson", { fieldName });
  }

  try {
    JSON.parse(value);
  } catch (e) {
    return i18n.t("validation.mustBeValidJson", { fieldName });
  }
};

export const regexp = (fieldName, value) => {
  try {
    new RegExp(value);
  } catch (err) {
    return i18n.t("validation.mustBeValidRegex", { fieldName });
  }
};
