.sidebar-menu {
  flex: 1;
  display: flex;
  max-height: calc(100vh - var(--header-height));

  &__navigation {
    width: calc(var(--menu-sidebar-width));
    display: flex;
    transition: border 400ms ease-out;

    & .main-menu {
      background: var(--color-neutral-background);

      &__item {
        background: var(--color-neutral-background);

        &_active {
          background: var(--color-primary-emphasis-subtle);
          pointer-events: all;
        }
      }
    }
  }

  &__content {
    flex: 1;
    overflow: auto;
    padding: 2rem;
  }
}
