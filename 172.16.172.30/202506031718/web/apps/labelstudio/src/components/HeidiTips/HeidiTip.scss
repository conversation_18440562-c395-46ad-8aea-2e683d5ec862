.heidy-tip {
  &__content {
    padding: 1rem;
    border: 1px solid var(--color-neutral-border);
    font-size: 14px;
    border-radius: 8px;
    box-shadow: 0 2px 6px 0 rgb(0 0 0 / 10%);
    box-sizing: border-box;
    background: var(--color-neutral-surface);
  }

  &__header {
    display: flex;
    width: 100%;
    min-height: 24px;
    margin-bottom: 8px;
    flex-direction: row;
    justify-content: space-between;
  }

  &__title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.15px;
    color:  var(--color-neutral-content);
  }

  &__text {
    line-height: 20px;
    letter-spacing: 0.25px;
    color:  var(--color-neutral-content-subtler);
  }

  &__link {
    color: var(--color-primary-content);
    font-weight: 500;
    display: block;

    &:hover {
      color: var(--color-primary-content-hover);
      text-decoration: underline;
    }

    &::before {
      content: " ";
    }
  }

  &__dismiss {
    width: 24px;
    height: 24px;
    padding: 0;
    border: none;
    display: flex;
    box-shadow: none;
    justify-content: center;
    align-items: center;

    svg {
      width: 14px;
      height: 14px;


      .spike-fill {
        fill: var(--color-neutral-surface);
        display: none;
      }
      
      .spike-stroke {
        fill: var(--color-neutral-border);
        display: none;
      }
    }

    svg > path {
      stroke: var(--color-neutral-content-subtlest);
    }

    &:hover {
      background: var(--color-primary-emphasis-subtle);

      svg path {
        stroke: var(--color-primary-surface-content);
      }
    }
  }

  &__heidi {
    margin-top: -12px;
    padding-left: 16px;
    pointer-events: none;
    color: var(--color-neutral-border);

    svg {
      path.spike-stroke {
        color: red;
      }

    }
  }
}