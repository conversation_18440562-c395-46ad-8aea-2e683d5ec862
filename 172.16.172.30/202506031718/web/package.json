{"name": "@humansignal/source", "version": "0.0.0", "license": "MIT", "scripts": {"ui:serve": "nx storybook storybook", "ui:test:component": "nx test-component ui", "lint": "biome check --apply .", "lint-scss": "yarn stylelint '**/*.scss' --fix", "ls:dev": "nx run labelstudio:serve:development", "ls:watch": "nx run labelstudio:build:development --watch", "ls:build": "nx run labelstudio:build:production", "ls:unit": "nx run labelstudio:unit", "ls:e2e": "nx run labelstudio-e2e:e2e", "lsf:watch": "nx run editor:build:development --watch", "lsf:serve": "FRONTEND_HOSTNAME=http://localhost:3000 MODE=standalone nx run editor:serve:development", "lsf:e2e": "cd libs/editor/tests/e2e && yarn test", "lsf:e2e:parallel": "cd libs/editor/tests/e2e && yarn test:parallel", "lsf:e2e:ci": "cd libs/editor/tests/e2e && yarn test:ci", "lsf:integration": "nx run editor:integration", "lsf:integration:watch": "nx run editor:integration --watch", "lsf:unit": "nx run editor:unit", "dm:watch": "nx run datamanager:build:development --watch", "dm:unit": "nx run datamanager:unit", "build": "NODE_ENV=production yarn ls:build", "version:libs": "nx run-many --target=version", "docs": "SCHEMA_JSON_PATH=$PWD/libs/core/src/lib/utils/schema/tags.json; nx docs editor && biome check --apply $SCHEMA_JSON_PATH", "watch": "NODE_ENV=development BUILD_NO_SERVER=true yarn ls:watch", "dev": "NODE_ENV=development BUILD_NO_SERVER=true yarn ls:dev", "test:e2e": "yarn ls:e2e && yarn lsf:e2e", "test:integration": "yarn lsf:integration", "test:unit": "nx run-many --target=unit", "test:coverage": "yarn test:unit --coverage", "test:affected": "nx affected --target=test:unit --base=develop", "test:watch": "yarn test:unit --all --watch", "test:watch:affected": "yarn test --watch", "postinstall": "npx -y copy-files-from-to", "design-tokens": "nx design-tokens ui", "extract-antd-no-reset": "nx extract-antd-no-reset editor", "storybook:serve": "nx storybook storybook", "storybook:build": "nx build-storybook storybook", "playground:serve": "FRONTEND_HOSTNAME=http://localhost:4200 MODE=standalone-playground nx run playground:serve:development", "playground:build": "NODE_ENV=production MODE=standalone-playground nx run playground:build:production && mv dist/apps/playground/playground-assets/index.html dist/apps/playground/index.html"}, "husky": {"hooks": {"post-checkout": "npx -y copy-files-from-to"}}, "private": true, "dependencies": {"@martel/audio-file-decoder": "^2.3.15", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.1.5", "@tanstack/query-core": "^5.66.0", "@tanstack/react-query": "^4", "@thi.ng/rle-pack": "^3.1.30", "antd": "^4.3.3", "chroma-js": "^2.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "codemirror": "^5.59.4", "d3": "^5.16.0", "d3-color": "3.1.0", "date-fns": "^2.20.1", "deep-equal": "^2.0.5", "emoji-regex": "^7.0.3", "history": "^4.10.1", "html-react-parser": "^1.2.4", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "insert-after": "^0.1.4", "jotai": "^2.11.3", "jotai-tanstack-query": "^0.9.0", "js-base64": "^3.7.7", "keymaster": "^1.6.2", "konva": "^8.1.3", "lodash.get": "^4.4.0", "lodash.ismatch": "^4.4.0", "lodash.throttle": "^4.1.1", "mobx": "^5.15.4", "mobx-react": "^6", "mobx-state-tree": "^3.16.0", "nanoid": "^3.3.8", "pako": "^2.1.0", "papaparse": "^5.4.1", "pleasejs": "^0.4.2", "rc-tree": "^5.7.8", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-codemirror2": "^7.2.1", "react-datepicker": "^3.6.0", "react-dom": "18.2.0", "react-hotkeys-hook": "^2.4.0", "react-i18next": "^15.5.2", "react-joyride": "^2.9.3", "react-konva": "^17.0.2-0", "react-konva-utils": "^0.2.0", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-singleton-hook": "^3.1.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.9", "react-window-infinite-loader": "^1.0.5", "sanitize-html": "^2.14.0", "shadcn": "^2.1.8", "storybook": "^8.5.0", "strman": "^2.0.1", "tailwind-merge": "^2.6.0", "wavesurfer.js": "^6.0.1", "xpath-range": "^1.1.1", "zod": "^3.22.4"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-proposal-class-properties": "^7.12.13", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.12.17", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-private-methods": "^7.22.5", "@babel/plugin-transform-private-property-in-object": "^7.22.11", "@babel/plugin-transform-runtime": "^7.12.15", "@babel/preset-env": "^7.12.13", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.13.0", "@babel/runtime": "^7.26.10", "@biomejs/biome": "1.9.4", "@chromatic-com/storybook": "^3", "@cypress/code-coverage": "^3.12.9", "@cypress/webpack-preprocessor": "^5.17.0", "@nx/cypress": "17.0.3", "@nx/jest": "17.0.3", "@nx/js": "17.2.8", "@nx/react": "17.0.3", "@nx/storybook": "^20.3.2", "@nx/webpack": "17.0.3", "@nx/workspace": "17.0.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@sentry/browser": "^8.33", "@sentry/react": "^8.33", "@storybook/addon-essentials": "7.6.10", "@storybook/addon-interactions": "^7.5.3", "@storybook/addon-links": "7.6.10", "@storybook/addon-styling-webpack": "1.0.0", "@storybook/core-server": "7.6.10", "@storybook/icons": "^1.4.0", "@storybook/react-webpack5": "7.6.10", "@storybook/test": "^8.5.0", "@storybook/test-runner": "^0.13.0", "@svgr/plugin-jsx": "^8.1.0", "@svgr/webpack": "^8.1.0", "@swc-node/register": "~1.6.7", "@swc/cli": "~0.1.62", "@swc/core": "1.3.96", "@tailwindcss/postcss": "^4.0.7", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "^8.0.1", "@types/chroma-js": "^2.1.3", "@types/jest": "^29.4.0", "@types/keymaster": "^1.6.33", "@types/mini-css-extract-plugin": "^2.5.1", "@types/node": "18.14.2", "@types/react": "18.2.33", "@types/react-dom": "18.2.14", "@types/react-router": "^5.1.7", "@types/react-router-dom": "^5.1.7", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@types/sanitize-html": "^2.13.0", "@types/sinon": "^17.0.3", "@types/strman": "^2.0.0", "autoprefixer": "^10.4.20", "babel-jest": "^29.4.1", "babel-loader": "^8.2.2", "babel-plugin-import": "^1.13.0", "babel-plugin-remove-webpack": "^1.1.0", "babel-plugin-transform-class-properties": "^6.24.1", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "chai": "^4.3.7", "css-loader": "^5.0.1", "css-minimizer-webpack-plugin": "^3.0.2", "cypress": "^13.0.0", "cypress-image-snapshot": "^4.0.1", "cypress-multi-reporters": "^1.6.2", "cypress-parallel": "^0.12.0", "cypress-plugin-snapshots": "^1.4.4", "cypress-terminal-report": "^5.1.1", "dotenv-defaults": "^2.0.2", "esbuild": "0.25.0", "husky": "^8.0.3", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "jest-fetch-mock": "^3.0.3", "jsdoc-to-markdown": "8.0.1", "jsdom": "~22.1.0", "loader-utils": "^3.2.1", "lodash": "4.17.21", "mini-css-extract-plugin": "^2.7.6", "nx": "17.0.3", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0", "postcss": "^8.4.49", "postcss-import": "^16.1.0", "postcss-loader": "^7.3.3", "proper-lockfile": "^4.1.2", "react-refresh": "^0.10.0", "sass": "^1.55.0", "sass-loader": "^11.0.1", "send": "0.19.0", "shallow-equal": "^1.2.1", "sinon": "^17.0.1", "source-map-loader": "^1.1.3", "style-loader": "^3.3.3", "stylelint": "^16.9.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-config-tailwindcss": "^0.0.7", "tailwindcss": "^3.x", "toml": "^3.0.0", "truncate-middle": "^1.0.6", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "4.8.3", "url-loader": "^4.1.1", "webpack": "^5.94.0", "webpack-cli": "^5.0.1", "webpack-merge": "^5.9.0", "yargs": "^17.7.1"}, "resolutions": {"@babel/traverse": "^7.24.1", "@cypress/request": "3.0.0", "body-parser": "^1.20.3", "axios": "^1.8.2", "cookie": "0.7.0", "**/bin-check/**/cross-spawn": "6.0.6", "d3-color": "3.1.0", "debug": "4.3.1", "diff": "3.5.0", "ejs": "3.1.10", "esbuild": "0.25.0", "express": ">=4.19.2", "express/path-to-regexp": "0.1.12", "follow-redirects": "^1.15.6", "ip": ">=2.0.1", "jpeg-js": "0.4.4", "lodash": "4.17.21", "send": "0.19.0", "serialize-javascript": "^6.0.2", "merge": "2.1.1", "webpack-dev-middleware": ">=5.3.4", "ws": "8.17.1", "xml2js": "0.5.0", "jsonwebtoken": "9.0.0", "moment": "2.29.4", "qs": "6.5.3", "@babel/helpers": "^7.26.10", "@babel/runtime": "^7.26.10"}, "copyFiles": [{"from": "./node_modules/@martel/audio-file-decoder/decode-audio.wasm", "to": "./node_modules/@martel/audio-file-decoder/dist/decode-audio.wasm"}], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}